# 🗂️ HyAIAgent - Public方法/变量协调字典

## 🎯 阶段说明
- **第一阶段**: 基础AI问答系统 ✅已完成
- **第二阶段**: 智能任务管理系统 ✅已完成
- **第三阶段**: 文档处理和分析系统 ✅已完成
- **第四阶段**: 网络搜索和信息检索系统 🔄进行中 (步骤4.3已完成)

## 📚 字典说明

### 🎯 核心目的
统一管理多文件间的Public方法和变量命名，避免开发过程中的调用混乱和命名冲突。

### 🔄 使用流程
1. **步骤开始前**: 仔细阅读字典中已有的类、方法、变量定义
2. **开发过程中**: 严格按照字典中的命名和接口进行开发
3. **步骤完成后**: 立即将新增的public方法和变量添加到字典中
4. **命名冲突**: 如发现命名冲突，优先使用字典中已定义的名称

### ⚠️ 重要规则
- **每个步骤开始前必须读取此字典**
- **每个步骤完成后必须更新此字典**
- **严格按照字典中的方法签名进行调用**
- **新增方法必须包含完整的参数和返回值类型**

---

## 📋 类和方法字典

### 1. ConfigManager类 (core/config_manager.py)

**类描述**: 配置管理器，负责加载和管理应用配置

#### Public方法
```python
def __init__(self, config_path: str = "config.json") -> None:
    """初始化配置管理器"""

def load_config(self) -> None:
    """加载配置文件"""

def load_env(self) -> None:
    """加载环境变量"""

def get_default_config(self) -> Dict[str, Any]:
    """获取默认配置"""

def save_config(self) -> bool:
    """保存配置文件"""

def get(self, key_path: str, default: Any = None) -> Any:
    """获取配置值（支持点号分隔的路径）"""

def set(self, key_path: str, value: Any) -> bool:
    """设置配置值"""

def get_ai_config(self, provider: Optional[str] = None) -> Dict[str, Any]:
    """获取AI提供商配置"""

def validate_config(self) -> bool:
    """验证配置的完整性"""
```

#### Public属性
```python
config_path: Path  # 配置文件路径
config: Dict[str, Any]  # 配置数据字典
```

---

### 2. SimpleAIClient类 (core/ai_client.py)

**类描述**: AI客户端，负责与OpenAI API通信

#### Public方法
```python
def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1",
             model: str = "gpt-3.5-turbo", max_tokens: int = 1000,
             temperature: float = 0.7, timeout: int = 30) -> None:
    """初始化AI客户端"""

def chat(self, message: str, system_prompt: Optional[str] = None) -> str:
    """发送消息并获取AI回复"""

def chat_stream(self, message: str, system_prompt: Optional[str] = None):
    """流式发送消息并获取AI回复"""

def clear_history(self) -> None:
    """清空对话历史"""

def get_history(self) -> List[Dict[str, str]]:
    """获取对话历史"""

def get_history_summary(self) -> str:
    """获取对话历史摘要"""

def set_model(self, model: str) -> None:
    """设置模型"""

def set_temperature(self, temperature: float) -> None:
    """设置温度参数"""

def set_max_tokens(self, max_tokens: int) -> None:
    """设置最大token数"""

def get_config(self) -> Dict[str, Any]:
    """获取当前配置"""

def test_connection(self) -> bool:
    """测试API连接"""
```

#### Public属性
```python
api_key: str  # API密钥
base_url: str  # API基础URL
model: str  # 模型名称
max_tokens: int  # 最大token数
temperature: float  # 温度参数
timeout: int  # 超时时间
conversation_history: List[Dict[str, str]]  # 对话历史
client: openai.OpenAI  # OpenAI客户端实例
```

---

### 3. KVStore类 (core/kv_store.py)

**类描述**: 轻量级KV数据库，使用TinyDB实现

#### Public方法
```python
def __init__(self, db_path: str = "data/kv_store.json",
             auto_cleanup: bool = True, cleanup_interval: int = 3600) -> None:
    """初始化KV数据库"""

def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """设置键值对"""

def get(self, key: str, default: Any = None) -> Any:
    """获取键值"""

def delete(self, key: str) -> bool:
    """删除键值对"""

def exists(self, key: str) -> bool:
    """检查键是否存在且未过期"""

def keys(self) -> List[str]:
    """获取所有有效键名"""

def clear(self) -> bool:
    """清空所有数据"""

def size(self) -> int:
    """获取有效记录数量"""

def cleanup_expired(self) -> int:
    """清理过期记录"""

def get_stats(self) -> Dict[str, Any]:
    """获取数据库统计信息"""

def backup(self, backup_path: str) -> bool:
    """备份数据库"""

def restore(self, backup_path: str, clear_existing: bool = False) -> bool:
    """从备份恢复数据库"""

def close(self) -> None:
    """关闭数据库连接"""
```

#### Public属性
```python
db_path: Path  # 数据库文件路径
auto_cleanup: bool  # 是否启用自动清理
cleanup_interval: int  # 清理间隔（秒）
last_cleanup: float  # 上次清理时间
db: TinyDB  # TinyDB数据库实例
query: Query  # TinyDB查询对象
```

---

### 4. PromptManager类 (core/prompt_manager.py)

**类描述**: 提示词管理器，负责加载和管理提示词模板

#### Public方法
```python
def __init__(self, prompts_dir: str = "prompts") -> None:
    """初始化提示词管理器"""

def get_global_prompt(self, name: str) -> Optional[str]:
    """获取全局提示词"""

def get_template_prompt(self, name: str, variables: Optional[Dict[str, Any]] = None) -> Optional[str]:
    """获取模板提示词并渲染"""

def add_prompt(self, name: str, content: str, prompt_type: str = "global") -> bool:
    """添加新的提示词"""

def update_prompt(self, name: str, content: str, prompt_type: str = "global") -> bool:
    """更新提示词"""

def delete_prompt(self, name: str, prompt_type: str = "global") -> bool:
    """删除提示词"""

def list_prompts(self, prompt_type: Optional[str] = None) -> List[str]:
    """列出所有提示词"""

def get_prompt_info(self, name: str, prompt_type: str = "global") -> Optional[Dict[str, Any]]:
    """获取提示词信息"""

def reload_prompts(self) -> None:
    """重新加载所有提示词"""

def get_stats(self) -> Dict[str, Any]:
    """获取提示词管理器统计信息"""
```

#### Public属性
```python
prompts_dir: Path  # 提示词根目录
global_dir: Path  # 全局提示词目录
templates_dir: Path  # 模板提示词目录
prompt_cache: Dict[str, Dict[str, Any]]  # 提示词缓存
jinja_env: Environment  # Jinja2环境
```

---

### 5. ChatWindow类 (ui/chat_window.py)

**类描述**: 主聊天窗口，PyQt6界面类

#### Public方法
```python
def __init__(self) -> None:
    """初始化聊天窗口"""

def setup_ui(self) -> None:
    """设置用户界面"""

def setup_menu_bar(self) -> None:
    """设置菜单栏"""

def setup_status_bar(self) -> None:
    """设置状态栏"""

def setup_ai_client(self) -> None:
    """设置AI客户端"""

def setup_connections(self) -> None:
    """设置信号连接"""

def send_message(self) -> None:
    """发送消息"""

def append_message(self, sender: str, message: str) -> None:
    """添加消息到聊天显示区域"""

def on_ai_response(self, response: str) -> None:
    """处理AI响应"""

def on_ai_error(self, error: str) -> None:
    """处理AI错误"""

def on_ai_finished(self) -> None:
    """AI响应完成"""

def new_chat(self) -> None:
    """新建对话"""

def save_chat(self) -> None:
    """保存对话"""

def show_settings(self) -> None:
    """显示设置对话框"""

def show_about(self) -> None:
    """显示关于对话框"""

def closeEvent(self, event) -> None:
    """窗口关闭事件"""
```

#### Public属性
```python
config_manager: ConfigManager  # 配置管理器
kv_store: KVStore  # KV数据库
prompt_manager: PromptManager  # 提示词管理器
ai_client: SimpleAIClient  # AI客户端
ai_thread: AIResponseThread  # AI响应线程
chat_display: QTextEdit  # 聊天显示区域
input_field: QLineEdit  # 输入框
send_button: QPushButton  # 发送按钮
status_label: QLabel  # 状态标签
```

#### 信号定义
```python
# ChatWindow类没有自定义信号
```

---

### 6. AIResponseThread类 (ui/chat_window.py)

**类描述**: AI响应线程，避免界面阻塞

#### Public方法
```python
def __init__(self, ai_client: SimpleAIClient, message: str, system_prompt: Optional[str] = None) -> None:
    """初始化AI响应线程"""

def run(self) -> None:
    """执行AI请求"""
```

#### Public属性
```python
ai_client: SimpleAIClient  # AI客户端
message: str  # 用户消息
system_prompt: Optional[str]  # 系统提示词
```

#### 信号定义
```python
response_received = pyqtSignal(str)  # 响应接收信号
error_occurred = pyqtSignal(str)  # 错误发生信号
```

---

### 7. SettingsDialog类 (ui/chat_window.py)

**类描述**: 设置对话框，用于配置AI和界面参数

#### Public方法
```python
def __init__(self, config_manager: ConfigManager, parent=None) -> None:
    """初始化设置对话框"""

def setup_ui(self) -> None:
    """设置界面"""

def load_settings(self) -> None:
    """加载设置"""

def save_settings(self) -> bool:
    """保存设置"""
```

#### Public属性
```python
config_manager: ConfigManager  # 配置管理器
model_combo: QComboBox  # 模型选择框
max_tokens_spin: QSpinBox  # 最大Token数输入框
temperature_spin: QDoubleSpinBox  # 温度参数输入框
font_size_spin: QSpinBox  # 字体大小输入框
auto_save_check: QCheckBox  # 自动保存复选框
ok_button: QPushButton  # 确定按钮
cancel_button: QPushButton  # 取消按钮
```

---

### 8. 主程序模块 (main.py)

**模块描述**: 应用程序主入口，负责初始化和启动

#### Public函数
```python
def setup_logging() -> bool:
    """设置日志系统"""

def check_dependencies() -> Tuple[bool, str]:
    """检查依赖项"""

def check_configuration() -> Tuple[bool, str]:
    """检查配置文件"""

def create_directories() -> bool:
    """创建必要的目录"""

def initialize_application() -> Tuple[bool, str]:
    """初始化应用程序"""

def main() -> int:
    """主函数"""
```

---

## 🔗 类间调用关系图

### 依赖关系
```
main.py
  └── ChatWindow
      ├── ConfigManager
      ├── SimpleAIClient
      └── ChatWorker
          └── SimpleAIClient

ConfigManager
  └── (独立模块)

SimpleAIClient
  └── (独立模块)

KVStore
  └── (独立模块)

PromptManager
  └── (独立模块)
```

### 调用流程
```
1. main.py 启动应用
2. ChatWindow 初始化
3. ChatWindow 创建 ConfigManager 实例
4. ChatWindow 创建 SimpleAIClient 实例
5. 用户发送消息时，ChatWindow 创建 ChatWorker
6. ChatWorker 调用 SimpleAIClient.chat() 方法
7. 返回结果显示在 ChatWindow
```

---

## 📝 更新记录

### 字典更新日志
| 时间 | 步骤 | 更新内容 | 更新者 |
|------|------|----------|--------|
| 2025-07-28 | 2.8 | 第二阶段开发完成，所有功能验收测试通过 | AI |
| 2025-07-28 | 2.7 | 更新所有18个类的方法签名，系统集成测试完成 | AI |
| 2025-01-27 | 1.2 | 更新ConfigManager类的方法和属性 | AI |
| 2025-01-27 | 1.1 | 创建初始字典结构 | AI |

### 命名规范
- **类名**: 使用PascalCase (如: ConfigManager)
- **方法名**: 使用snake_case (如: load_config)
- **属性名**: 使用snake_case (如: config_path)
- **常量名**: 使用UPPER_CASE (如: DEFAULT_CONFIG)
- **私有方法**: 以下划线开头 (如: _load_env)

---

## 🚨 AI更新指令

### 每完成一个步骤后必须执行：

1. **读取当前字典**: 确保了解已有的33个类的方法和变量定义
2. **更新对应类的方法和属性**: 添加新开发的public方法和属性
3. **检查命名冲突**: 确保新增的命名不与现有的冲突
4. **更新调用关系**: 如有新的类间调用关系，更新关系图
5. **记录更新日志**: 在更新记录中添加本次更新的内容

### 🌐 第四阶段特殊要求：

1. **网络搜索集成**: 新增的搜索相关类必须与现有的TaskManager和ExecutionEngine集成
2. **缓存系统兼容**: 搜索缓存必须与现有的KVStore系统协调工作
3. **异步操作统一**: 所有搜索操作必须采用与现有文件操作一致的异步模式
4. **安全管理集成**: 网络搜索功能必须遵循现有的SecurityManager安全规范
5. **提示词系统扩展**: 搜索专用提示词必须与现有的PromptManager系统集成

### 更新格式示例：

```python
def method_name(self, param1: str, param2: int = 0) -> bool:
    """
    方法功能描述
    
    Args:
        param1 (str): 参数1描述
        param2 (int, optional): 参数2描述. Defaults to 0.
    
    Returns:
        bool: 返回值描述
    """
```

---

## 🚀 第二阶段新增类（开发中）

### 9. TaskManager类 (core/task_manager.py)

**类描述**: 智能任务管理器，负责任务分解、执行计划创建和进度监控

#### Public方法
```python
def __init__(self, ai_client: SimpleAIClient, prompt_manager: PromptManager,
             kv_store: KVStore) -> None:
    """初始化任务管理器"""

async def decompose_task(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> List[Task]:
    """将用户输入分解为可执行任务"""

async def create_execution_plan(self, tasks: List[Task], plan_name: str = "",
                              parallel_execution: bool = False) -> ExecutionPlan:
    """创建任务执行计划"""

async def execute_plan(self, plan: ExecutionPlan) -> ExecutionResult:
    """执行任务计划"""

async def monitor_progress(self, execution_id: str) -> ProgressInfo:
    """监控执行进度"""
```

#### Public属性
```python
ai_client: SimpleAIClient  # AI客户端实例
prompt_manager: PromptManager  # 提示词管理器实例
kv_store: KVStore  # KV数据库实例
active_plans: Dict[str, ExecutionPlan]  # 活跃的执行计划
task_registry: Dict[str, Task]  # 任务注册表
execution_results: Dict[str, List[ExecutionResult]]  # 执行结果
running_plans: Dict[str, bool]  # 运行中的计划状态
plan_progress: Dict[str, ProgressInfo]  # 计划进度信息
```

---

### 10. ExecutionEngine类 (core/execution_engine.py)

**类描述**: 任务执行引擎，负责单任务执行、错误处理和结果评估

#### Public方法
```python
def __init__(self, ai_client: SimpleAIClient, prompt_manager: PromptManager) -> None:
    """初始化执行引擎"""

async def execute_task(self, task: Task) -> ExecutionResult:
    """执行单个任务"""

async def handle_task_failure(self, task: Task, error: Exception) -> bool:
    """处理任务执行失败"""

async def evaluate_result(self, result: ExecutionResult) -> Dict[str, Any]:
    """评估执行结果"""

def get_execution_stats(self) -> Dict[str, Any]:
    """获取执行统计信息"""
```

#### Public属性
```python
ai_client: SimpleAIClient  # AI客户端实例
prompt_manager: PromptManager  # 提示词管理器实例
task_executors: Dict[TaskType, Callable]  # 任务执行器注册表
execution_stats: Dict[str, Any]  # 执行统计信息
```

---

### 11. DecisionEngine类 (core/decision_engine.py)

**类描述**: AI决策引擎，负责执行决策和策略调整

#### Public方法
```python
def __init__(self, ai_client: SimpleAIClient, prompt_manager: PromptManager) -> None:
    """初始化决策引擎"""

async def should_continue(self, context: ExecutionContext) -> bool:
    """判断是否继续执行"""

async def select_next_action(self, context: ExecutionContext) -> Action:
    """选择下一步行动"""

async def adjust_strategy(self, feedback: ExecutionFeedback) -> Dict[str, Any]:
    """根据反馈调整策略"""

def get_decision_stats(self) -> Dict[str, Any]:
    """获取决策统计信息"""
```

#### Public属性
```python
ai_client: SimpleAIClient  # AI客户端实例
prompt_manager: PromptManager  # 提示词管理器实例
decision_history: List[Dict[str, Any]]  # 决策历史
decision_rules: Dict[str, Any]  # 决策规则配置
performance_tracker: Dict[str, int]  # 性能指标
```

---

### 12. ContextManager类 (core/context_manager.py)

**类描述**: 上下文管理器，负责全局状态管理和数据传递

#### Public方法
```python
def __init__(self, kv_store: KVStore) -> None:
    """初始化上下文管理器"""

async def initialize_context(self, plan: ExecutionPlan) -> bool:
    """初始化执行上下文"""

async def update_current_task(self, task: Task) -> bool:
    """更新当前任务"""

async def add_completed_task(self, task: Task, result: ExecutionResult) -> bool:
    """添加已完成任务"""

async def add_failed_task(self, task: Task, error: str) -> bool:
    """添加失败任务"""

async def set_global_variable(self, key: str, value: Any) -> bool:
    """设置全局变量"""

def get_global_variable(self, key: str, default: Any = None) -> Any:
    """获取全局变量"""

async def create_snapshot(self) -> bool:
    """创建上下文快照"""

def get_context_summary(self) -> Dict[str, Any]:
    """获取上下文摘要"""

def get_performance_metrics(self) -> Dict[str, Any]:
    """获取性能指标"""

async def restore_context(self, plan_id: str) -> bool:
    """恢复执行上下文"""
```

#### Public属性
```python
kv_store: KVStore  # KV数据库实例
current_context: Dict[str, Any]  # 当前执行上下文
context_snapshots: List[ContextSnapshot]  # 上下文历史快照
auto_save_enabled: bool  # 自动保存开关
auto_save_interval: int  # 自动保存间隔
stats: Dict[str, int]  # 性能统计
```

---

### 13. TaskPrompts类 (prompts/task_prompts.py)

**类描述**: 任务特定提示词管理器，提供不同类型任务的专门提示词

#### Public方法
```python
def __init__(self) -> None:
    """初始化提示词模板"""

def get_prompt(self, task_type: TaskType, category: PromptCategory,
               context: Optional[Dict[str, Any]] = None) -> str:
    """获取特定任务类型和类别的提示词"""

def add_custom_prompt(self, task_type: TaskType, category: PromptCategory,
                     prompt_template: str) -> bool:
    """添加自定义提示词模板"""

def get_available_prompts(self) -> Dict[str, list]:
    """获取可用的提示词列表"""
```

#### Public属性
```python
prompts: Dict[str, str]  # 提示词模板字典
```

---

### 15. BaseOperation类 (operations/base_operation.py)

**类描述**: 操作基类，定义所有操作的通用接口

#### Public方法
```python
def __init__(self, operation_type: OperationType, name: str, description: str = "") -> None:
    """初始化操作"""

async def execute(self, **kwargs) -> OperationResult:
    """执行操作（抽象方法）"""

def validate_parameters(self, **kwargs) -> bool:
    """验证操作参数（抽象方法）"""

async def run(self, **kwargs) -> OperationResult:
    """运行操作（包含重试逻辑）"""

def cancel(self) -> bool:
    """取消操作"""

def get_status_info(self) -> Dict[str, Any]:
    """获取操作状态信息"""

def configure(self, timeout: Optional[int] = None, max_retries: Optional[int] = None,
             retry_delay: Optional[float] = None) -> None:
    """配置操作参数"""
```

#### Public属性
```python
operation_type: OperationType  # 操作类型
name: str  # 操作名称
description: str  # 操作描述
status: OperationStatus  # 操作状态
result: Optional[OperationResult]  # 操作结果
timeout: int  # 超时时间
max_retries: int  # 最大重试次数
retry_count: int  # 当前重试次数
```

---

### 16. SystemOperations类 (operations/system_operations.py)

**类描述**: 系统操作类，实现基础的系统级操作

#### Public方法
```python
def __init__(self) -> None:
    """初始化系统操作管理器"""

async def execute_file_operation(self, **kwargs) -> OperationResult:
    """执行文件操作"""

async def execute_process_operation(self, **kwargs) -> OperationResult:
    """执行进程操作"""

def get_available_operations(self) -> Dict[str, List[str]]:
    """获取可用操作列表"""
```

#### Public属性
```python
file_op: FileOperation  # 文件操作实例
process_op: ProcessOperation  # 进程操作实例
```

---

### 17. AutonomousAgent类 (core/autonomous_agent.py)

**类描述**: 自主执行代理，HyAIAgent的核心类，实现完整的自主思考和执行循环

#### Public方法
```python
def __init__(self, config_path: str = "config/config.yaml") -> None:
    """初始化自主代理"""

async def start(self, initial_request: str = None) -> str:
    """启动代理"""

async def stop(self) -> bool:
    """停止代理"""

async def pause(self) -> bool:
    """暂停代理"""

async def resume(self) -> bool:
    """恢复代理"""

async def process_request(self, request: str) -> Dict[str, Any]:
    """处理用户请求"""

def get_status(self) -> Dict[str, Any]:
    """获取代理状态"""

def get_detailed_status(self) -> Dict[str, Any]:
    """获取详细状态信息"""
```

#### Public属性
```python
agent_id: str  # 代理ID
state: str  # 代理状态 (idle/thinking/planning/executing/evaluating/paused/error/stopped)
is_running: bool  # 是否运行中
current_session_id: Optional[str]  # 当前会话ID
stats: Dict[str, Any]  # 性能统计
task_manager: TaskManager  # 任务管理器
execution_engine: ExecutionEngine  # 执行引擎
decision_engine: DecisionEngine  # 决策引擎
context_manager: ContextManager  # 上下文管理器
```

---

### 18. HyAIAgentCLI类 (cli_main.py)

**类描述**: HyAIAgent命令行界面，提供用户交互和代理控制

#### Public方法
```python
def __init__(self) -> None:
    """初始化CLI"""

async def start(self) -> None:
    """启动CLI"""

async def cleanup(self) -> None:
    """清理资源"""
```

#### Public属性
```python
agent: Optional[AutonomousAgent]  # 代理实例
running: bool  # 是否运行中
```

---

## 🔗 第二阶段类间调用关系图

### 依赖关系（预期）
```
第一阶段基础类
  ├── ConfigManager (独立)
  ├── SimpleAIClient (独立)
  ├── KVStore (独立)
  └── PromptManager (独立)

第二阶段新增类
  ├── TaskManager
  │   ├── 依赖: SimpleAIClient, PromptManager, KVStore
  │   └── 调用: ExecutionEngine, DecisionEngine
  ├── ExecutionEngine
  │   ├── 依赖: SimpleAIClient, PromptManager, KVStore
  │   └── 调用: SystemOperations, BaseOperation
  ├── DecisionEngine
  │   ├── 依赖: SimpleAIClient, PromptManager
  │   └── 调用: ContextManager
  ├── ContextManager
  │   └── 依赖: KVStore
  ├── TaskPrompts
  │   └── 依赖: 无 (独立)
  ├── BaseOperation & SystemOperations
  │   └── 依赖: 无 (独立)
  └── AutonomousAgent (核心集成类)
      ├── 依赖: 所有上述类
      └── 调用: TaskManager, ExecutionEngine, DecisionEngine, ContextManager
```

### 调用流程（实际）
```
自主执行循环:
1. 用户输入 → AutonomousAgent.process_request()
2. AutonomousAgent → ContextManager.initialize_context()
3. AutonomousAgent → TaskManager.decompose_task()
4. TaskManager → SimpleAIClient.chat() (任务分析)
5. AutonomousAgent → TaskManager.create_execution_plan()
6. AutonomousAgent → TaskManager.execute_plan()
7. ExecutionEngine → SystemOperations.execute()
8. AutonomousAgent → DecisionEngine.should_continue()
9. DecisionEngine → ContextManager.get_current_context()
10. 循环继续或结束 → AutonomousAgent状态管理

CLI交互流程:
1. 用户命令 → HyAIAgentCLI._handle_special_commands()
2. 用户请求 → HyAIAgentCLI._process_user_request()
3. CLI → AutonomousAgent.process_request()
4. 进入自主执行循环...
```

---

**📌 重要提醒：此字典是多文件协调开发的核心工具，必须严格维护，确保所有开发都基于此字典进行！**

**🚨 第二阶段开发规则：每个步骤开始前必须读取此字典，每个步骤完成后必须立即更新对应类的方法和属性定义！**

---

## 🚀 第四阶段开发规则

### 📋 第四阶段：网络搜索集成 (2025-07-29 开始)

#### 🎯 开发目标
集成网络搜索能力，让AI能够获取实时信息、进行网络调研，并结合本地操作完成更复杂的信息处理任务。

#### 🗂️ 协调字典使用规范（第四阶段专用）

**每个步骤开始前必须执行：**
1. **读取完整字典** - 仔细阅读所有已定义的33个类、方法、变量
2. **理解现有架构** - 明确前三阶段已有的类结构和调用关系
3. **规划新类接口** - 设计新类与现有类的集成方式
4. **避免命名冲突** - 确保新增的命名不与现有的冲突

**每个步骤完成后必须执行：**
1. **立即更新字典** - 将新开发的类完整添加到字典中
2. **详细记录接口** - 包含所有public方法的完整签名和参数说明
3. **更新调用关系** - 如有新的类间调用，更新依赖关系图
4. **记录更新日志** - 在字典更新记录中添加详细的更新内容

#### 🚨 第四阶段强制规则
- **步骤开始**: 必须先读取字典，了解现有33个类的接口定义
- **开发过程**: 严格按照字典中的方法签名进行调用
- **步骤结束**: 必须先更新字典，再在进度控制文件中标记完成
- **命名统一**: 新增类必须遵循现有的命名规范
- **接口兼容**: 确保新类与现有类的无缝集成

#### 📊 第四阶段预期新增类
预计将新增以下核心类（具体实现后更新）：
- TavilySearchClient (operations/search_operations.py)
- SearchOperations (operations/search_operations.py)
- ContentProcessor (operations/content_processor.py)
- InformationAnalyzer (operations/information_analyzer.py)
- DataIntegrator (operations/data_integrator.py)
- SearchCache (cache/search_cache.py)
- ContentCache (cache/content_cache.py)
- SearchUtils (utils/search_utils.py)
- ContentUtils (utils/content_utils.py)
- ValidationUtils (utils/validation_utils.py)

**🔄 更新提醒：以上类列表将在实际开发过程中根据具体实现情况进行调整和完善。**

---

## 🚀 第四阶段已完成类（步骤4.1完成）

### 34. TavilySearchClient类 (operations/search_operations.py) ✅已完成

**类描述**: Tavily搜索API客户端，负责与Tavily搜索引擎的API通信

#### Public方法
```python
def __init__(self, api_key: str, base_url: str = "https://api.tavily.com") -> None:
    """初始化Tavily搜索客户端"""

async def __aenter__(self):
    """异步上下文管理器入口"""

async def __aexit__(self, exc_type, exc_val, exc_tb):
    """异步上下文管理器出口"""

async def search(self,
                query: str,
                search_depth: str = "basic",
                max_results: int = 5,
                include_domains: Optional[List[str]] = None,
                exclude_domains: Optional[List[str]] = None,
                timeout: int = 30) -> SearchResponse:
    """执行搜索请求"""

async def close(self) -> None:
    """关闭客户端会话"""
```

#### Public属性
```python
api_key: str  # Tavily API密钥
base_url: str  # API基础URL
session: Optional[aiohttp.ClientSession]  # HTTP会话
request_count: int  # 请求计数
last_request_time: float  # 最后请求时间
rate_limit_delay: float  # 速率限制延迟
```

#### 依赖关系
- 使用 `aiohttp` 进行异步HTTP请求
- 使用 `asyncio` 进行异步操作和速率限制
- 使用 `time` 进行时间管理和速率控制

---

### 35. SearchOperations类 (operations/search_operations.py) ✅已完成

**类描述**: 网络搜索操作管理器，提供完整的搜索功能包括缓存、统计和错误处理

#### Public方法
```python
def __init__(self,
             config_manager: ConfigManager,
             security_manager: Optional[SecurityManager] = None,
             kv_store: Optional[KVStore] = None) -> None:
    """初始化搜索操作管理器"""

async def search_web(self,
                    query: str,
                    search_depth: str = "basic",
                    max_results: int = 5,
                    use_cache: bool = True,
                    **kwargs) -> SearchResponse:
    """执行网络搜索"""

async def get_search_stats(self) -> Dict[str, Any]:
    """获取搜索统计信息"""

async def close(self) -> None:
    """关闭搜索操作管理器"""
```

#### Public属性
```python
config_manager: ConfigManager  # 配置管理器
security_manager: Optional[SecurityManager]  # 安全管理器
kv_store: Optional[KVStore]  # 键值存储
search_config: Dict[str, Any]  # 搜索配置
tavily_config: Dict[str, Any]  # Tavily配置
tavily_client: TavilySearchClient  # Tavily客户端
search_stats: Dict[str, Any]  # 搜索统计信息
```

#### 依赖关系
- 依赖 `ConfigManager` 进行配置管理
- 可选依赖 `SecurityManager` 进行安全验证
- 可选依赖 `KVStore` 进行搜索结果缓存
- 依赖 `TavilySearchClient` 进行实际搜索操作
- 使用 `hashlib` 进行缓存键生成
- 使用 `json` 进行数据序列化

---

### 36. SearchResult类 (operations/search_operations.py) ✅已完成

**类描述**: 搜索结果数据模型，表示单个搜索结果

#### Public方法
```python
def to_dict(self) -> Dict[str, Any]:
    """转换为字典格式"""
```

#### Public属性
```python
title: str  # 搜索结果标题
url: str  # 搜索结果URL
content: str  # 搜索结果内容
score: float  # 搜索结果评分
published_date: Optional[str]  # 发布日期
source: str  # 来源标识
```

---

### 37. SearchResponse类 (operations/search_operations.py) ✅已完成

**类描述**: 搜索响应数据模型，包含完整的搜索响应信息

#### Public方法
```python
def to_dict(self) -> Dict[str, Any]:
    """转换为字典格式"""
```

#### Public属性
```python
query: str  # 搜索查询
results: List[SearchResult]  # 搜索结果列表
total_results: int  # 结果总数
search_time: float  # 搜索用时
timestamp: datetime  # 搜索时间戳
search_depth: str  # 搜索深度
```

---

### 38. SearchCache类 (operations/search_cache.py) ✅已完成

**类描述**: 搜索缓存管理器，负责搜索结果的缓存管理，包括TTL缓存、LRU缓存和持久化缓存

#### Public方法
```python
def __init__(self, config_manager: ConfigManager, security_manager: Optional[SecurityManager] = None, kv_store: Optional[KVStore] = None) -> None:
    """初始化搜索缓存管理器"""

async def get_search_result(self, query: str, **kwargs) -> Optional[SearchResponse]:
    """获取搜索结果缓存"""

async def set_search_result(self, query: str, result: SearchResponse, ttl_seconds: Optional[int] = None, **kwargs) -> bool:
    """设置搜索结果缓存"""

async def get_processed_result(self, query: str, **kwargs) -> Optional[ProcessedResponse]:
    """获取处理结果缓存"""

async def set_processed_result(self, query: str, result: ProcessedResponse, ttl_seconds: Optional[int] = None, **kwargs) -> bool:
    """设置处理结果缓存"""

async def clear_cache(self, cache_type: Optional[str] = None) -> int:
    """清理缓存"""

async def get_cache_stats(self) -> CacheStats:
    """获取缓存统计信息"""

async def close(self) -> None:
    """关闭缓存管理器"""

def _generate_cache_key(self, query: str, **kwargs) -> str:
    """生成缓存键"""

def _calculate_size(self, data: Any) -> int:
    """计算数据大小（字节）"""

async def _ensure_cache_space(self, required_size: int) -> None:
    """确保缓存有足够空间"""

async def _evict_lru_entry(self) -> None:
    """驱逐最近最少使用的条目"""

def _start_cleanup_task(self) -> None:
    """启动清理任务"""

async def _cleanup_loop(self) -> None:
    """清理循环"""

async def _cleanup_expired_entries(self) -> None:
    """清理过期条目"""
```

#### Public属性
```python
config_manager: ConfigManager  # 配置管理器
security_manager: Optional[SecurityManager]  # 安全管理器
kv_store: Optional[KVStore]  # 键值存储
cache_config: Dict[str, Any]  # 缓存配置
max_entries: int  # 最大条目数
default_ttl: int  # 默认TTL（秒）
max_size_bytes: int  # 最大大小（字节）
cleanup_interval: int  # 清理间隔（秒）
enable_persistent: bool  # 是否启用持久化
memory_cache: Dict[str, CacheEntry]  # 内存缓存
stats: Dict[str, int]  # 统计信息
```

#### 依赖模块
- 使用 `hashlib` 进行缓存键生成
- 使用 `json` 进行数据序列化
- 使用 `datetime` 进行时间管理
- 使用 `asyncio` 进行异步清理任务

---

### 39. CacheEntry类 (operations/search_cache.py) ✅已完成

**类描述**: 缓存条目数据模型，表示单个缓存条目的完整信息

#### Public方法
```python
def is_expired(self) -> bool:
    """检查是否已过期"""

def is_valid(self) -> bool:
    """检查是否有效（未过期且数据完整）"""

def to_dict(self) -> Dict[str, Any]:
    """转换为字典格式"""

@classmethod
def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
    """从字典创建缓存条目"""
```

#### Public属性
```python
key: str  # 缓存键
data: Dict[str, Any]  # 缓存数据
created_at: datetime  # 创建时间
expires_at: datetime  # 过期时间
access_count: int  # 访问次数
last_accessed: datetime  # 最后访问时间
cache_type: str  # 缓存类型（'search' 或 'processed'）
size_bytes: int  # 数据大小（字节）
```

---

### 40. CacheStats类 (operations/search_cache.py) ✅已完成

**类描述**: 缓存统计信息数据模型，包含缓存性能和使用情况的统计数据

#### Public属性
```python
total_entries: int  # 总条目数
total_size_bytes: int  # 总大小（字节）
hit_count: int  # 命中次数
miss_count: int  # 未命中次数
eviction_count: int  # 驱逐次数
expired_count: int  # 过期次数
hit_rate: float  # 命中率
average_entry_size: float  # 平均条目大小
oldest_entry_age: float  # 最老条目年龄（秒）
newest_entry_age: float  # 最新条目年龄（秒）
```

---

## 🚀 第三阶段开发规则

### 📋 第三阶段：基础操作模块开发 (2025-07-28 开始)

#### 🎯 开发目标
扩展AI的操作能力，实现文件系统操作和基础的本地环境交互，让AI能够自主完成文件处理相关的工作。

#### 🗂️ 协调字典使用规范（第三阶段专用）

**每个步骤开始前必须执行：**
1. **读取完整字典** - 仔细阅读所有已定义的类、方法、变量
2. **理解现有架构** - 明确第一、二阶段已有的类结构和调用关系
3. **规划新类接口** - 设计新类与现有类的集成方式
4. **避免命名冲突** - 确保新增的命名不与现有的冲突

**每个步骤完成后必须执行：**
1. **立即更新字典** - 将新开发的类完整添加到字典中
2. **详细记录接口** - 包含所有public方法的完整签名和参数说明
3. **更新调用关系** - 如有新的类间调用，更新依赖关系图
4. **记录更新日志** - 在字典更新记录中添加详细的更新内容

#### 🚨 第三阶段强制规则
- **步骤开始**: 必须先读取字典，了解现有18个类的接口定义
- **开发过程**: 严格按照字典中的方法签名进行调用
- **步骤结束**: 必须先更新字典，再在进度控制文件中标记完成
- **命名统一**: 新增类必须遵循现有的命名规范
- **接口兼容**: 确保新类与现有类的无缝集成

#### 📊 第三阶段预期新增类
预计将新增以下核心类（具体实现后更新）：
- SecurityManager (operations/security_manager.py)
- FileOperations (operations/file_operations.py)
- DocumentProcessor (operations/document_processor.py)
- FileAnalyzer (operations/file_analyzer.py)
- FileUtils (utils/file_utils.py)
- PathUtils (utils/path_utils.py)
- FormatConverter (utils/format_converter.py)

**🔄 更新提醒：以上类列表将在实际开发过程中根据具体实现情况进行调整和完善。**

---

## 🚀 第三阶段新增类（开发中）

### 19. SecurityManager类 (operations/security_manager.py) ✅已完成

**类描述**: 文件操作安全管理器，负责路径验证、权限控制和操作审计

#### Public方法
```python
def __init__(self, workspace_path: str = "./workspace",
             config_path: str = "config/file_security.json") -> None:
    """初始化安全管理器"""

def validate_path(self, file_path: str) -> bool:
    """验证文件路径安全性"""

def check_file_type(self, file_path: str) -> bool:
    """检查文件类型是否允许"""

def check_file_permissions(self, file_path: str, operation: str = "read") -> bool:
    """检查文件权限"""

def check_file_size(self, file_path: str) -> bool:
    """检查文件大小是否超过限制"""

def validate_operation(self, file_path: str, operation: str = "read") -> Dict[str, Any]:
    """综合验证文件操作的安全性"""

def get_audit_log(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """获取审计日志"""

def clear_audit_log(self) -> None:
    """清空审计日志"""

def get_security_stats(self) -> Dict[str, Any]:
    """获取安全统计信息"""

def update_config(self, new_config: Dict[str, Any]) -> bool:
    """更新安全配置"""
```

#### Public属性
```python
workspace_path: Path  # 工作目录路径
config_path: Path  # 安全配置文件路径
config: Dict[str, Any]  # 安全配置字典
audit_log: List[Dict[str, Any]]  # 审计日志列表
```

#### Private方法
```python
def _load_security_config(self) -> Dict[str, Any]:
    """加载安全配置文件"""

def _save_default_config(self, config: Dict[str, Any]) -> None:
    """保存默认配置文件"""

def _audit_operation(self, operation: str, file_path: str, status: str, details: str = "") -> None:
    """记录操作审计日志"""
```

### 20. FileOperations类 (operations/file_operations.py) ✅已完成

**类描述**: 文件操作核心类，提供安全的异步文件操作功能

#### Public方法
```python
def __init__(self, workspace_path: str = "./workspace",
             security_manager: Optional[SecurityManager] = None) -> None:
    """初始化文件操作管理器"""

async def read_file(self, file_path: str, encoding: Optional[str] = None) -> Dict[str, Any]:
    """异步读取文件内容"""

async def write_file(self, file_path: str, content: str,
                    encoding: str = 'utf-8', mode: str = 'w') -> Dict[str, Any]:
    """异步写入文件内容"""

async def delete_file(self, file_path: str) -> Dict[str, Any]:
    """异步删除文件"""

async def list_files(self, directory: str = "", pattern: str = "*",
                    recursive: bool = False) -> Dict[str, Any]:
    """列出目录中的文件"""

async def search_files(self, query: str, search_content: bool = False,
                      file_pattern: str = "*") -> Dict[str, Any]:
    """搜索文件"""

async def advanced_search_files(self, query: str, search_options: Dict[str, Any] = None) -> Dict[str, Any]:
    """高级文件搜索功能，支持正则表达式、内容搜索、文件属性过滤等"""

async def filter_files(self, filter_options: Dict[str, Any]) -> Dict[str, Any]:
    """文件过滤功能，支持多种过滤条件和排序选项"""

def get_operation_stats(self) -> Dict[str, Any]:
    """获取操作统计信息"""

def reset_stats(self) -> None:
    """重置操作统计"""
```

#### Public属性
```python
workspace_path: Path  # 工作目录路径
security_manager: SecurityManager  # 安全管理器实例
operation_stats: Dict[str, int]  # 操作统计信息
```

#### Private方法
```python
async def _detect_encoding(self, file_path: Path) -> str:
    """检测文件编码"""

async def _get_file_info(self, file_path: Path, relative_path: Path) -> Dict[str, Any]:
    """获取文件信息"""
```

### 21. PathUtils类 (operations/path_utils.py) ✅已完成

**类描述**: 路径处理工具类，提供跨平台的路径处理和安全检查工具

#### Public方法
```python
@staticmethod
def normalize_path(path: Union[str, Path]) -> str:
    """规范化路径"""

@staticmethod
def is_safe_path(path: Union[str, Path], base_path: Union[str, Path]) -> bool:
    """检查路径是否安全（防止路径遍历攻击）"""

@staticmethod
def contains_dangerous_patterns(path: str) -> bool:
    """检查路径是否包含危险模式"""

@staticmethod
def is_sensitive_path(path: Union[str, Path]) -> bool:
    """检查是否为系统敏感路径"""

@staticmethod
def get_relative_path(path: Union[str, Path], base_path: Union[str, Path]) -> str:
    """获取相对路径"""

@staticmethod
def join_paths(*paths: Union[str, Path]) -> str:
    """安全地连接路径"""

@staticmethod
def split_path(path: Union[str, Path]) -> Tuple[str, str]:
    """分割路径为目录和文件名"""

@staticmethod
def get_path_depth(path: Union[str, Path]) -> int:
    """获取路径深度"""

@staticmethod
def is_absolute_path(path: Union[str, Path]) -> bool:
    """检查是否为绝对路径"""

@staticmethod
def ensure_directory_exists(path: Union[str, Path]) -> bool:
    """确保目录存在"""

@staticmethod
def get_common_path(paths: List[Union[str, Path]]) -> str:
    """获取多个路径的公共父路径"""

@staticmethod
def validate_filename(filename: str) -> bool:
    """验证文件名是否合法"""

@staticmethod
def sanitize_filename(filename: str) -> str:
    """清理文件名，移除非法字符"""
```

#### Public属性
```python
DANGEROUS_PATTERNS: List[str]  # 危险路径模式
SENSITIVE_PATHS: List[str]  # 系统敏感路径
```

### 22. FileUtils类 (operations/file_utils.py) ✅已完成

**类描述**: 文件处理工具类，提供文件处理和格式化工具

#### Public方法
```python
@staticmethod
def format_file_size(size_bytes: int) -> str:
    """格式化文件大小为人类可读格式"""

@staticmethod
def get_file_extension(file_path: Union[str, Path]) -> str:
    """获取文件扩展名（小写）"""

@staticmethod
def get_file_category(file_path: Union[str, Path]) -> str:
    """获取文件类别"""

@staticmethod
def is_text_file(file_path: Union[str, Path]) -> bool:
    """判断是否为文本文件"""

@staticmethod
def is_image_file(file_path: Union[str, Path]) -> bool:
    """判断是否为图片文件"""

@staticmethod
def is_dangerous_file(file_path: Union[str, Path]) -> bool:
    """判断是否为危险文件类型"""

@staticmethod
def get_mime_type(file_path: Union[str, Path]) -> str:
    """获取文件MIME类型"""

@staticmethod
def calculate_file_hash(file_path: Union[str, Path], algorithm: str = "md5") -> Optional[str]:
    """计算文件哈希值"""

@staticmethod
def get_file_info(file_path: Union[str, Path]) -> Dict[str, any]:
    """获取文件详细信息"""

@staticmethod
def compare_files(file1: Union[str, Path], file2: Union[str, Path]) -> Dict[str, any]:
    """比较两个文件"""

@staticmethod
def get_files_by_extension(directory: Union[str, Path], extension: str, recursive: bool = False) -> List[str]:
    """获取指定扩展名的所有文件"""

@staticmethod
def get_directory_size(directory: Union[str, Path]) -> int:
    """计算目录总大小"""

@staticmethod
def clean_filename_for_url(filename: str) -> str:
    """清理文件名以适用于URL"""
```

#### Public属性
```python
FILE_CATEGORIES: Dict[str, List[str]]  # 文件类型分类
DANGEROUS_EXTENSIONS: List[str]  # 危险文件扩展名
```

### 23. DirectoryOperations类 (operations/directory_operations.py) ✅已完成

**类描述**: 目录操作类，提供安全的异步目录操作功能

#### Public方法
```python
async def create_directory(self, directory_path: str, parents: bool = True) -> Dict[str, Any]:
    """异步创建目录"""

async def delete_directory(self, directory_path: str, recursive: bool = False) -> Dict[str, Any]:
    """异步删除目录"""

async def copy_directory(self, source_path: str, destination_path: str, overwrite: bool = False) -> Dict[str, Any]:
    """异步复制目录"""

async def move_directory(self, source_path: str, destination_path: str, overwrite: bool = False) -> Dict[str, Any]:
    """异步移动目录"""

async def get_directory_info(self, directory_path: str = "") -> Dict[str, Any]:
    """获取目录详细信息"""

async def list_directory_tree(self, directory_path: str = "", max_depth: int = 3) -> Dict[str, Any]:
    """获取目录树结构"""

def get_operation_stats(self) -> Dict[str, Any]:
    """获取操作统计信息"""

def reset_stats(self) -> None:
    """重置操作统计"""
```

#### Public属性
```python
workspace_path: Path  # 工作目录路径
security_manager: SecurityManager  # 安全管理器实例
operation_stats: Dict[str, int]  # 操作统计信息
```

### 24. DocumentProcessor类 (operations/document_processor.py) ✅已完成

**类描述**: 全面的文档处理器，支持多种文档格式的读取、解析、转换和分析

#### Public方法
```python
def __init__(self, workspace_path: str = "./workspace",
             security_manager: Optional[SecurityManager] = None,
             file_operations: Optional[FileOperations] = None) -> None:
    """初始化文档处理器"""

async def process_document(self, file_path: str, format_hint: Optional[str] = None,
                         encoding: Optional[str] = None) -> Dict[str, Any]:
    """处理单个文档文件"""

async def batch_process_documents(self, file_patterns: List[str],
                                max_concurrent: int = 5) -> Dict[str, Any]:
    """批量处理文档"""

async def search_in_documents(self, query: str, file_patterns: List[str] = ["*"],
                            case_sensitive: bool = False) -> Dict[str, Any]:
    """在文档中搜索内容"""

def get_processing_stats(self) -> Dict[str, Any]:
    """获取处理统计信息"""

def reset_stats(self) -> None:
    """重置处理统计"""
```

#### Private方法
```python
def _detect_document_format(self, file_path: Path) -> str:
    """检测文档格式"""

async def _parse_document(self, content: str, doc_format: str, file_path: Path) -> Dict[str, Any]:
    """解析文档内容"""

async def _parse_text_document(self, content: str) -> Dict[str, Any]:
    """解析文本文档"""

async def _parse_data_document(self, content: str, extension: str) -> Dict[str, Any]:
    """解析数据文档（JSON、CSV、XML）"""

async def _parse_config_document(self, content: str, extension: str) -> Dict[str, Any]:
    """解析配置文档（INI、YAML）"""

async def _parse_markup_document(self, content: str, extension: str) -> Dict[str, Any]:
    """解析标记文档（HTML）"""

async def _extract_metadata(self, file_path: Path, doc_format: str, parsed_content: Dict[str, Any]) -> Dict[str, Any]:
    """提取文档元数据"""

def _analyze_json_structure(self, data: Any) -> Dict[str, Any]:
    """分析JSON结构"""

def _analyze_xml_structure(self, root: ET.Element) -> Dict[str, Any]:
    """分析XML结构"""

def _analyze_yaml_structure(self, data: Any) -> Dict[str, Any]:
    """分析YAML结构"""
```

#### Public属性
```python
workspace_path: Path  # 工作空间路径
security_manager: SecurityManager  # 安全管理器实例
file_operations: FileOperations  # 文件操作实例
file_utils: FileUtils  # 文件工具实例
supported_formats: Dict[str, List[str]]  # 支持的文档格式
processing_stats: Dict[str, Any]  # 处理统计信息
```

#### 支持的文档格式
- **文本文档**: .txt, .md, .rst, .rtf
- **数据文档**: .json, .xml, .csv, .tsv
- **配置文档**: .ini, .yaml, .yml, .toml
- **标记文档**: .html, .htm, .xml

#### 依赖关系
- 依赖 `SecurityManager` 进行安全验证
- 依赖 `FileOperations` 进行文件读写操作
- 依赖 `FileUtils` 进行文件工具支持
- 使用 `json`, `csv`, `xml.etree.ElementTree` 进行数据解析
- 可选依赖 `yaml`, `configparser`, `BeautifulSoup` 进行特定格式解析

---

## 🚀 第三阶段新增类（步骤3.13完成）

### 32. FileAnalyzer类 (operations/file_analyzer.py) ✅已完成

**类描述**: 综合文件分析器，提供全面的文件分析功能，包括基础信息分析、内容分析、结构分析、安全分析、文件比较和相似文件查找

#### Public方法
```python
def __init__(self, workspace_path: str = "./workspace",
             security_manager: Optional[SecurityManager] = None,
             file_operations: Optional[FileOperations] = None,
             document_processor: Optional[DocumentProcessor] = None,
             text_analyzer: Optional[TextAnalyzer] = None,
             config_processor: Optional[ConfigProcessor] = None) -> None:
    """初始化文件分析器"""

async def analyze_file(self, file_path: str, analysis_types: List[str] = None) -> Dict[str, Any]:
    """分析单个文件

    Args:
        file_path (str): 文件路径
        analysis_types (List[str], optional): 分析类型列表，可选值：
            - "basic": 基础信息分析
            - "content": 内容分析
            - "structure": 结构分析
            - "security": 安全分析
            - "comprehensive": 综合分析（包含所有类型）

    Returns:
        Dict[str, Any]: 分析结果，包含各种分析维度的数据
    """

async def batch_analyze_files(self, file_patterns: List[str], analysis_types: List[str] = None,
                             max_concurrent: int = 5) -> Dict[str, Any]:
    """批量分析文件

    Args:
        file_patterns (List[str]): 文件模式列表（支持通配符）
        analysis_types (List[str], optional): 分析类型列表
        max_concurrent (int): 最大并发数

    Returns:
        Dict[str, Any]: 批量分析结果
    """

async def compare_files(self, file1: str, file2: str, comparison_types: List[str] = None) -> Dict[str, Any]:
    """比较两个文件

    Args:
        file1 (str): 第一个文件路径
        file2 (str): 第二个文件路径
        comparison_types (List[str], optional): 比较类型列表

    Returns:
        Dict[str, Any]: 文件比较结果
    """

async def find_similar_files(self, target_file: str, search_patterns: List[str],
                           similarity_threshold: float = 0.7, max_results: int = 10) -> Dict[str, Any]:
    """查找相似文件

    Args:
        target_file (str): 目标文件路径
        search_patterns (List[str]): 搜索模式列表
        similarity_threshold (float): 相似度阈值
        max_results (int): 最大结果数量

    Returns:
        Dict[str, Any]: 相似文件查找结果
    """

def get_analysis_stats(self) -> Dict[str, Any]:
    """获取分析统计信息

    Returns:
        Dict[str, Any]: 统计信息
    """

def reset_stats(self) -> None:
    """重置统计信息"""
```

#### Public属性
```python
workspace_path: Path  # 工作目录路径
security_manager: SecurityManager  # 安全管理器
file_operations: FileOperations  # 文件操作实例
document_processor: DocumentProcessor  # 文档处理器
text_analyzer: TextAnalyzer  # 文本分析器
config_processor: ConfigProcessor  # 配置处理器
analysis_stats: Dict[str, Any]  # 分析统计信息
supported_analysis_types: List[str]  # 支持的分析类型
```

---

## 📝 第三阶段新增提示词文件（步骤3.15完成）

### 专用任务提示词 (prompts/tasks/) ✅已完成

**文件描述**: 为不同类型的任务提供专门的AI提示词，提高任务执行的准确性和效率

#### 提示词文件列表
```
prompts/tasks/
├── file_operations.md      # 文件操作专用提示词
├── document_analysis.md    # 文档分析专用提示词
└── batch_processing.md     # 批量处理专用提示词
```

#### 文件操作提示词 (file_operations.md)
- **角色定义**: 专业的文件操作助手
- **安全原则**: 路径安全、文件类型安全、操作权限控制
- **操作类型**: 基础文件操作、目录操作、文件信息查询、批量操作
- **处理流程**: 任务分析→安全验证→操作执行→结果验证
- **响应格式**: 标准化的JSON格式响应

#### 文档分析提示词 (document_analysis.md)
- **角色定义**: 专业的文档分析专家
- **分析维度**: 基础信息、内容结构、语义内容、质量评估
- **分析类型**: 快速分析、内容分析、结构分析、深度分析
- **处理流程**: 文档预处理→多层次分析→结果整合→后处理优化
- **输出格式**: 结构化的分析结果JSON

#### 批量处理提示词 (batch_processing.md)
- **角色定义**: 专业的批量处理专家
- **处理模式**: 并发处理、智能调度、进度监控、错误恢复
- **操作类型**: 文件批量操作、数据批量处理、格式批量转换
- **处理流程**: 任务规划→资源准备→批量执行→结果汇总
- **性能优化**: 并发控制、内存优化、I/O优化

#### 质量标准
- **内容完整性**: 每个提示词文件包含角色定义、处理流程、注意事项、目标等关键章节
- **格式规范性**: 使用Markdown格式，包含表情符号、代码块、结构化内容
- **实用性**: 提供具体的操作指导和最佳实践
- **可扩展性**: 支持根据具体需求进行定制和扩展

---

## 🚀 第三阶段新增类（步骤3.7完成）

### 25. TextAnalyzer类 (operations/text_analyzer.py)

**类描述**: 综合文本分析器，提供全面的文本分析功能，包括基础统计、词频分析、模式提取、语言检测、结构分析、质量评估和内容分类

#### Public方法
```python
def __init__(self, workspace_path: str) -> None:
    """初始化文本分析器"""

async def analyze_text(self, text: str, analysis_options: Optional[Dict[str, bool]] = None) -> Dict[str, Any]:
    """分析文本内容

    Args:
        text (str): 要分析的文本
        analysis_options (Dict[str, bool], optional): 分析选项配置

    Returns:
        Dict[str, Any]: 分析结果，包含各种分析维度的数据
    """

async def analyze_file(self, file_path: str, analysis_options: Optional[Dict[str, bool]] = None) -> Dict[str, Any]:
    """分析文件内容

    Args:
        file_path (str): 文件路径
        analysis_options (Dict[str, bool], optional): 分析选项配置

    Returns:
        Dict[str, Any]: 文件分析结果
    """

async def batch_analyze_files(self, file_patterns: List[str],
                            analysis_options: Optional[Dict[str, bool]] = None,
                            max_concurrent: int = 5) -> Dict[str, Any]:
    """批量分析多个文件

    Args:
        file_patterns (List[str]): 文件模式列表
        analysis_options (Dict[str, bool], optional): 分析选项配置
        max_concurrent (int): 最大并发数

    Returns:
        Dict[str, Any]: 批量分析结果
    """

def get_analysis_stats(self) -> Dict[str, Any]:
    """获取分析统计信息

    Returns:
        Dict[str, Any]: 统计信息
    """

def reset_stats(self) -> None:
    """重置分析统计"""
```

#### Public属性
```python
workspace_path: str                    # 工作空间路径
security_manager: SecurityManager     # 安全管理器实例
file_operations: FileOperations       # 文件操作实例
analysis_stats: Dict[str, Any]        # 分析统计信息
text_patterns: Dict[str, re.Pattern]  # 文本模式正则表达式
stop_words: Dict[str, Set[str]]       # 停用词集合
```

#### 分析功能
- **基础统计**: 字符数、单词数、行数、句子数、段落数等
- **词频分析**: 中英文词频统计、停用词过滤、词汇多样性分析
- **模式提取**: 邮箱、URL、电话号码、日期时间、IP地址等模式识别
- **语言检测**: 中英文语言检测、多语言文本识别
- **结构分析**: Markdown标题、列表、代码块等结构元素识别
- **质量评估**: 可读性评分、质量问题识别、文本质量等级
- **内容分类**: 技术文档、业务文档、学术文档等内容类型分类

#### 支持的文件格式
- **文本文件**: .txt, .md, .rst, .log
- **代码文件**: .py, .js, .html, .css, .json
- **文档文件**: .csv, .xml, .yaml, .yml

#### 依赖关系
- 依赖 `SecurityManager` 进行安全验证
- 依赖 `FileOperations` 进行文件读写操作
- 使用 `re`, `hashlib`, `asyncio`, `collections.Counter` 进行文本处理
- 支持中英文文本分析和多语言检测

---

## 26. ConfigProcessor类 (operations/config_processor.py) ✅已完成

**类描述**: 配置文件处理器，提供全面的配置文件处理功能，支持多种格式的配置文件读取、解析、验证、转换和管理

#### Public方法
```python
async def process_config(self, file_path: str, format_hint: Optional[str] = None,
                        validate_schema: Optional[Dict[str, Any]] = None,
                        replace_env_vars: bool = True) -> Dict[str, Any]:
    """处理配置文件，支持JSON、YAML、INI、TOML格式"""

async def convert_config_format(self, source_path: str, target_path: str,
                               target_format: str, preserve_comments: bool = False) -> Dict[str, Any]:
    """转换配置文件格式"""

async def merge_configs(self, config_paths: List[str], output_path: Optional[str] = None,
                       merge_strategy: str = 'deep') -> Dict[str, Any]:
    """合并多个配置文件，支持深度合并、浅合并、覆盖合并"""

async def backup_config(self, config_path: str, backup_dir: Optional[str] = None) -> Dict[str, Any]:
    """备份配置文件"""

async def validate_config_file(self, config_path: str, schema: Dict[str, Any]) -> Dict[str, Any]:
    """验证配置文件"""

def get_processing_stats(self) -> Dict[str, Any]:
    """获取处理统计信息"""

def reset_stats(self) -> None:
    """重置统计信息"""
```

#### Private方法
```python
def _detect_config_format(self, file_path: Union[str, Path]) -> str:
    """检测配置文件格式"""

async def _parse_config(self, content: str, config_format: str) -> Dict[str, Any]:
    """解析配置内容"""

def _replace_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
    """替换环境变量"""

async def _validate_config(self, config: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
    """验证配置"""

async def _convert_config_data(self, config: Dict[str, Any], target_format: str,
                              preserve_comments: bool = False) -> str:
    """转换配置数据格式"""

def _deep_merge_configs(self, configs: List[Dict[str, Any]]) -> Dict[str, Any]:
    """深度合并配置"""

def _shallow_merge_configs(self, configs: List[Dict[str, Any]]) -> Dict[str, Any]:
    """浅合并配置"""

def _override_merge_configs(self, configs: List[Dict[str, Any]]) -> Dict[str, Any]:
    """覆盖合并配置"""

def _check_nested_key(self, config: Dict[str, Any], key_path: str) -> bool:
    """检查嵌套键是否存在"""

def _get_nested_value(self, config: Dict[str, Any], key_path: str) -> Any:
    """获取嵌套值"""

def _apply_custom_rule(self, config: Dict[str, Any], rule: Dict[str, Any]) -> Dict[str, Any]:
    """应用自定义验证规则"""

def _update_stats(self, status: str, format_type: Optional[str] = None) -> None:
    """更新处理统计"""
```

#### Public属性
```python
workspace_path: str  # 工作空间路径
security_manager: SecurityManager  # 安全管理器实例
file_operations: FileOperations  # 文件操作实例
supported_formats: List[str]  # 支持的配置文件格式
processing_stats: Dict[str, Any]  # 处理统计信息
env_var_pattern: Pattern  # 环境变量替换模式
```

#### 支持的配置格式
- **JSON**: .json 格式配置文件
- **YAML**: .yaml, .yml 格式配置文件（需要PyYAML库）
- **INI**: .ini, .cfg, .conf 格式配置文件
- **TOML**: .toml 格式配置文件（需要toml库）

#### 主要功能
- **多格式支持**: 支持JSON、YAML、INI、TOML等主流配置格式
- **环境变量替换**: 支持${VAR}格式的环境变量替换
- **配置验证**: 基于Schema的配置验证，支持自定义验证规则
- **格式转换**: 跨格式配置文件转换
- **配置合并**: 多种合并策略（深度、浅层、覆盖）
- **备份功能**: 安全的配置文件备份
- **统计信息**: 详细的处理统计和性能监控

#### 依赖关系
- 依赖 `SecurityManager` 进行安全验证
- 依赖 `FileOperations` 进行文件读写操作
- 可选依赖 `PyYAML` 库支持YAML格式
- 可选依赖 `toml` 库支持TOML格式
- 使用 `json`, `configparser`, `re`, `datetime` 进行配置处理

---

## 27. FormatConverter类 (utils/format_converter.py) ✅已完成

**类描述**: 格式转换工具类，提供多种文件格式之间的转换功能，支持文本、配置、数据格式的相互转换

#### Public方法
```python
async def convert_file(self, source_path: str, target_path: str,
                      target_format: str = None, encoding: str = 'utf-8',
                      preserve_structure: bool = True, validation: bool = True) -> Dict[str, Any]:
    """转换单个文件格式"""

async def batch_convert_files(self, file_mappings: List[Dict[str, str]],
                             target_format: str = None, encoding: str = 'utf-8',
                             max_concurrent: int = 5) -> Dict[str, Any]:
    """批量转换文件格式"""

async def convert_encoding(self, file_path: str, target_encoding: str = 'utf-8',
                          backup: bool = True) -> Dict[str, Any]:
    """转换文件编码"""

def get_supported_formats(self) -> Dict[str, List[str]]:
    """获取支持的格式列表"""

def get_conversion_stats(self) -> Dict[str, Any]:
    """获取转换统计信息"""

def reset_stats(self) -> None:
    """重置转换统计"""
```

#### Private方法
```python
def _detect_file_format(self, file_path: Union[str, Path]) -> str:
    """检测文件格式"""

async def _parse_content(self, content: str, format_type: str) -> Any:
    """解析文件内容"""

async def _convert_to_format(self, data: Any, target_format: str,
                           preserve_structure: bool = True) -> str:
    """将数据转换为目标格式"""

def _xml_to_dict(self, element: ET.Element) -> Dict[str, Any]:
    """将XML元素转换为字典"""

def _dict_to_xml(self, data: Any, root_name: str = 'root') -> str:
    """将字典转换为XML"""

async def _validate_conversion(self, source_path: str, target_path: str,
                             source_format: str, target_format: str) -> Dict[str, Any]:
    """验证转换结果"""

def _update_stats(self, status: str, source_format: Optional[str] = None,
                 target_format: Optional[str] = None) -> None:
    """更新转换统计"""
```

#### Public属性
```python
workspace_path: Path  # 工作空间路径
security_manager: SecurityManager  # 安全管理器实例
file_operations: FileOperations  # 文件操作实例
supported_formats: Dict[str, List[str]]  # 支持的格式映射
conversion_stats: Dict[str, Any]  # 转换统计信息
```

#### 支持的格式类别
- **文本格式**: txt, md, rst, log
- **配置格式**: json, yaml, yml, ini, cfg, conf, toml
- **数据格式**: json, xml, csv, tsv
- **标记格式**: html, xml, md

#### 主要功能
- **多格式转换**: 支持文本、配置、数据格式之间的相互转换
- **编码转换**: 支持不同字符编码之间的转换（UTF-8、GBK等）
- **批量转换**: 支持多文件并发转换，可控制并发数
- **格式检测**: 自动检测文件格式，支持扩展名映射
- **结构保持**: 转换时可选择保持原始数据结构
- **转换验证**: 自动验证转换结果的正确性
- **统计跟踪**: 详细的转换统计和性能监控
- **备份功能**: 编码转换时自动创建备份文件

#### 依赖关系
- 依赖 `SecurityManager` 进行安全验证
- 依赖 `FileOperations` 进行文件读写操作
- 可选依赖 `PyYAML` 库支持YAML格式转换
- 可选依赖 `toml` 库支持TOML格式转换
- 使用 `json`, `csv`, `xml.etree.ElementTree`, `configparser` 进行格式处理
- 使用 `chardet` 进行编码检测
- 使用 `shutil` 进行文件备份操作

---

### 28. BatchProcessor类 (operations/batch_processor.py)

**类描述**: 批量文件处理器，提供高效的批量文件操作功能，支持并发处理和进度监控

#### Public方法
```python
def __init__(self, workspace_path: str = "./workspace",
             security_manager: Optional[SecurityManager] = None,
             file_operations: Optional[FileOperations] = None,
             max_workers: int = 4) -> None:
    """初始化批量处理器"""

async def batch_rename_files(self, file_patterns: List[str],
                            rename_rule: Dict[str, Any]) -> Dict[str, Any]:
    """批量重命名文件"""

async def batch_copy_files(self, source_patterns: List[str],
                          destination_dir: str,
                          copy_options: Dict[str, Any] = None) -> Dict[str, Any]:
    """批量复制文件"""

async def batch_delete_files(self, file_patterns: List[str],
                            delete_options: Dict[str, Any] = None) -> Dict[str, Any]:
    """批量删除文件"""

async def batch_move_files(self, source_patterns: List[str],
                          destination_dir: str,
                          move_options: Dict[str, Any] = None) -> Dict[str, Any]:
    """批量移动文件"""

async def batch_process_content(self, file_patterns: List[str],
                               content_processor: Callable[[str], str],
                               process_options: Dict[str, Any] = None) -> Dict[str, Any]:
    """批量处理文件内容"""

async def batch_calculate_checksums(self, file_patterns: List[str],
                                   algorithm: str = "md5") -> Dict[str, Any]:
    """批量计算文件校验和"""

def get_processing_stats(self) -> Dict[str, Any]:
    """获取处理统计信息"""

def _reset_stats(self) -> None:
    """重置处理统计"""
```

#### Public属性
```python
workspace_path: Path              # 工作目录路径
security_manager: SecurityManager # 安全管理器实例
file_operations: FileOperations   # 文件操作实例
max_workers: int                  # 最大并发工作线程数
processing_stats: Dict[str, Any]  # 处理统计信息
```

#### 主要功能
- **批量重命名**: 支持模式重命名、索引编号、大小写转换、文本替换
- **批量复制**: 支持保持目录结构、覆盖控制、备份创建、并发处理
- **批量移动**: 支持目录结构保持、冲突处理、性能优化
- **批量删除**: 支持安全删除、自动备份、批量确认
- **内容处理**: 支持自定义内容处理函数、编码检测、预览模式
- **校验和计算**: 支持MD5、SHA1、SHA256算法、并发计算
- **进度监控**: 详细的操作统计、性能监控、错误跟踪
- **并发控制**: 可配置的并发数量、信号量控制、资源管理

#### 依赖关系
- 依赖 `SecurityManager` 进行安全验证
- 依赖 `FileOperations` 进行文件操作
- 使用 `asyncio` 进行异步并发处理
- 使用 `concurrent.futures.ThreadPoolExecutor` 进行线程池管理
- 使用 `hashlib` 进行校验和计算
- 使用 `shutil` 进行文件复制移动操作
- 使用 `fnmatch` 进行文件模式匹配
- 使用 `pathlib.Path` 进行路径处理

---

## 📊 统计信息

- **总类数**: 28个
- **总方法数**: 168个
- **总属性数**: 48个
- **最后更新**: 2025-07-29 08:00

---

### 29. FileOperationTaskExecutor类 (operations/task_integration.py) ✅已完成

**类描述**: 文件操作任务执行器，将文件操作功能与任务管理系统集成

#### Public方法
```python
def __init__(self, workspace_path: str = "./workspace") -> None:
    """初始化文件操作任务执行器"""

async def execute_task(self, task: Task) -> ExecutionResult:
    """执行文件操作任务"""

async def _execute_file_read(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """执行文件读取操作"""

async def _execute_file_write(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """执行文件写入操作"""

async def _execute_file_delete(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """执行文件删除操作"""

async def _execute_file_search(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """执行文件搜索操作"""

async def _execute_batch_checksum(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """执行批量校验和计算"""

async def _execute_batch_rename(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """执行批量重命名操作"""
```

#### Public属性
```python
workspace_path: Path  # 工作目录路径
security_manager: SecurityManager  # 安全管理器
file_operations: FileOperations  # 文件操作实例
batch_processor: BatchProcessor  # 批量处理器
document_processor: DocumentProcessor  # 文档处理器
text_analyzer: TextAnalyzer  # 文本分析器
config_processor: ConfigProcessor  # 配置处理器
task_executors: Dict[str, Callable]  # 任务执行器映射
```

---

### 30. TaskSystemIntegrator类 (operations/task_integration.py) ✅已完成

**类描述**: 任务系统集成器，提供完整的任务系统与文件操作集成功能

#### Public方法
```python
def __init__(self, task_manager: TaskManager, execution_engine: ExecutionEngine, workspace_path: str = "./workspace") -> None:
    """初始化任务系统集成器"""

def _register_file_operation_executors(self) -> None:
    """注册文件操作执行器"""

async def create_file_operation_task(self, operation_type: str, parameters: Dict[str, Any], description: str = "", priority: str = "NORMAL") -> Task:
    """创建文件操作任务"""

async def create_batch_file_operation_plan(self, operations: List[Dict[str, Any]], plan_name: str = "Batch File Operations", parallel_execution: bool = True) -> str:
    """创建批量文件操作计划"""

async def execute_file_operation_plan(self, plan_id: str) -> ExecutionResult:
    """执行文件操作计划"""

async def monitor_file_operation_progress(self, plan_id: str) -> Dict[str, Any]:
    """监控文件操作进度"""

def get_supported_operations(self) -> List[Dict[str, Any]]:
    """获取支持的操作类型列表"""
```

#### Public属性
```python
task_manager: TaskManager  # 任务管理器
execution_engine: ExecutionEngine  # 执行引擎
file_task_executor: FileOperationTaskExecutor  # 文件操作任务执行器
```

### 31. TestSystemIntegration类 (test/test_system_integration.py) ✅已完成

**类描述**: 系统集成测试类，提供端到端的系统集成测试功能

#### Public方法
```python
def setup_test_environment(self) -> Dict[str, Any]:
    """设置测试环境"""

async def test_end_to_end_file_read_workflow(self, setup_test_environment) -> None:
    """测试端到端文件读取工作流"""

async def test_end_to_end_file_write_workflow(self, setup_test_environment) -> None:
    """测试端到端文件写入工作流"""

async def test_complex_workflow_execution(self, setup_test_environment) -> None:
    """测试复杂工作流程执行"""

async def test_batch_task_execution(self, setup_test_environment) -> None:
    """测试批量任务执行"""

async def test_error_handling_and_recovery(self, setup_test_environment) -> None:
    """测试错误处理和恢复机制"""

async def test_concurrent_task_execution(self, setup_test_environment) -> None:
    """测试并发任务执行"""

async def test_performance_benchmarks(self, setup_test_environment) -> None:
    """测试性能基准"""

def test_supported_operations_coverage(self, setup_test_environment) -> None:
    """测试支持的操作类型覆盖度"""
```

### 32. PerformanceOptimizer类 (utils/performance_optimizer.py)

**类描述**: 性能优化器，负责监控系统性能、识别瓶颈、提供优化建议和执行自动优化

#### Public方法
```python
def __init__(self, workspace_path: str = "./workspace") -> None:
    """初始化性能优化器"""

async def collect_metrics(self) -> PerformanceMetrics:
    """收集当前性能指标"""

async def analyze_performance(self, metrics: PerformanceMetrics) -> List[OptimizationSuggestion]:
    """分析性能并生成优化建议"""

async def apply_optimizations(self, suggestions: List[OptimizationSuggestion]) -> Dict[str, Any]:
    """应用优化建议"""

async def get_performance_report(self) -> Dict[str, Any]:
    """生成性能报告"""

async def optimize_system(self) -> Dict[str, Any]:
    """执行系统优化"""

def get_optimization_stats(self) -> Dict[str, Any]:
    """获取优化统计信息"""
```

#### Public属性
```python
workspace_path: Path  # 工作空间路径
metrics_history: List[PerformanceMetrics]  # 性能指标历史
thresholds: Dict[str, float]  # 性能阈值配置
optimization_config: Dict[str, Any]  # 优化配置
stats: Dict[str, Any]  # 性能统计
```

### 33. PerformanceMonitor类 (utils/performance_monitor.py)

**类描述**: 性能监控器，负责实时监控系统性能，收集性能数据，触发告警和自动优化

#### Public方法
```python
def __init__(self, workspace_path: str = "./workspace", config: Optional[MonitoringConfig] = None) -> None:
    """初始化性能监控器"""

async def start_monitoring(self) -> bool:
    """开始性能监控"""

async def stop_monitoring(self) -> bool:
    """停止性能监控"""

def add_alert_callback(self, callback: Callable) -> None:
    """添加告警回调函数"""

def add_metrics_callback(self, callback: Callable) -> None:
    """添加指标回调函数"""

def get_current_status(self) -> Dict[str, Any]:
    """获取当前监控状态"""

async def get_performance_summary(self) -> Dict[str, Any]:
    """获取性能摘要"""
```

#### Public属性
```python
workspace_path: Path  # 工作空间路径
config: MonitoringConfig  # 监控配置
optimizer: PerformanceOptimizer  # 性能优化器
is_monitoring: bool  # 监控状态
current_metrics: Optional[PerformanceMetrics]  # 当前性能指标
metrics_history: List[PerformanceMetrics]  # 性能指标历史
alerts_history: List[Dict[str, Any]]  # 告警历史
```

---

### 41. BasicSearchOperation类 (operations/search_operations.py) ✅已完成

**类描述**: 基础搜索操作类，继承自BaseOperation，提供标准化的搜索接口和参数验证

#### Public方法
```python
def __init__(self, search_operations: SearchOperations) -> None:
    """初始化基础搜索操作"""

async def execute(self, **kwargs) -> OperationResult:
    """执行基础搜索操作"""

async def validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """验证搜索参数"""

def validate_parameters(self, **kwargs) -> bool:
    """验证操作参数（BaseOperation抽象方法实现）"""
```

#### Public属性
```python
search_operations: SearchOperations  # 搜索操作管理器实例
operation_type: OperationType  # 操作类型（NETWORK）
name: str  # 操作名称（"basic_search"）
description: str  # 操作描述
```

#### 依赖关系
- 继承自 `BaseOperation` 基础操作类
- 依赖 `SearchOperations` 进行实际搜索操作
- 使用 `OperationResult` 返回操作结果
- 使用 `OperationType.NETWORK` 标识操作类型

---

### 42. SearchToolkit类 (operations/search_operations.py) ✅已完成

**类描述**: 搜索工具包，提供便捷的搜索功能接口，包括快速搜索、高级搜索、批量搜索等功能

#### Public方法
```python
def __init__(self, config_manager: ConfigManager,
             security_manager: Optional[SecurityManager] = None,
             kv_store: Optional[KVStore] = None) -> None:
    """初始化搜索工具包"""

async def quick_search(self, query: str, max_results: int = 5) -> Dict[str, Any]:
    """快速搜索 - 使用默认参数进行简单搜索"""

async def advanced_search(self, query: str,
                        max_results: int = 10,
                        include_domains: Optional[List[str]] = None,
                        exclude_domains: Optional[List[str]] = None) -> Dict[str, Any]:
    """高级搜索 - 使用高级搜索深度和域名过滤"""

async def search_with_validation(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """带参数验证的搜索"""

async def batch_search(self, queries: List[str],
                      max_results_per_query: int = 5,
                      max_concurrent: int = 3) -> Dict[str, Any]:
    """批量搜索 - 并发执行多个搜索查询"""

async def get_search_statistics(self) -> Dict[str, Any]:
    """获取搜索统计信息"""

async def close(self) -> None:
    """关闭搜索工具包"""
```

#### Public属性
```python
search_operations: SearchOperations  # 搜索操作管理器
basic_search_op: BasicSearchOperation  # 基础搜索操作实例
```

#### 依赖关系
- 依赖 `SearchOperations` 进行搜索管理
- 依赖 `BasicSearchOperation` 进行基础搜索操作
- 依赖 `ConfigManager` 进行配置管理
- 可选依赖 `SecurityManager` 和 `KVStore`
- 使用 `asyncio.Semaphore` 进行并发控制

### 43. ContentProcessor类 (operations/content_processor.py) ✅已完成

**类描述**: 内容处理器，负责搜索结果的解析、清理和结构化处理

#### Public方法
```python
def __init__(self, config_manager: ConfigManager, security_manager: SecurityManager) -> None:
    """初始化内容处理器"""

async def process_search_response(self, search_response: SearchResponse) -> ProcessedResponse:
    """处理搜索响应，返回结构化的处理结果"""

async def extract_key_information(self, content: str) -> Dict[str, Any]:
    """提取内容中的关键信息"""

async def generate_summary(self, contents: List[str], max_length: int = 500) -> Dict[str, Any]:
    """生成多个内容的综合摘要"""

async def structure_data(self, raw_content: str) -> Dict[str, Any]:
    """将非结构化内容转换为结构化数据"""

async def detect_duplicates(self, contents: List[str], similarity_threshold: float = 0.8) -> Dict[str, Any]:
    """检测内容列表中的重复内容"""

async def batch_process_contents(self, contents: List[str], operations: List[str] = None) -> Dict[str, Any]:
    """批量处理多个内容"""

async def get_processing_stats(self) -> Dict[str, Any]:
    """获取处理统计信息"""

# ==================== 信息验证系统方法 (步骤4.8) ====================

async def validate_information(self, information: str, sources: List[str] = None,
                             validation_type: str = "comprehensive") -> Dict[str, Any]:
    """验证信息的准确性和可靠性"""

async def cross_validate_sources(self, information: str, sources: List[str]) -> Dict[str, Any]:
    """交叉验证多个信息源"""

async def verify_factual_claims(self, claims: List[str], context: str = None) -> Dict[str, Any]:
    """验证事实性声明"""

async def assess_information_quality(self, information: str,
                                   quality_criteria: List[str] = None) -> Dict[str, Any]:
    """评估信息质量"""

# ==================== 数据结构化处理方法 (步骤4.9) ====================

async def extract_structured_entities(self, content: str) -> Dict[str, Any]:
    """提取结构化实体信息"""

async def convert_to_json_schema(self, content: str, schema_type: str = "auto") -> Dict[str, Any]:
    """将内容转换为JSON Schema格式"""

async def extract_data_relationships(self, content: str) -> Dict[str, Any]:
    """提取数据关系"""

async def normalize_data_format(self, content: str, target_format: str = "standard") -> Dict[str, Any]:
    """标准化数据格式"""
```

#### Public属性
```python
processing_stats: Dict[str, Any]  # 处理统计信息
patterns: Dict[str, re.Pattern]   # 编译的正则表达式模式
```

#### 依赖关系
- 依赖 `ConfigManager` 进行配置管理
- 依赖 `SecurityManager` 进行安全管理
- 依赖 `SearchResult` 和 `SearchResponse` 数据模型
- 使用 `ProcessedContent` 和 `ProcessedResponse` 数据模型
- 使用 `loguru` 进行日志记录

---

## 📊 统计信息

- **总类数**: 46个
- **总方法数**: 410个
- **总属性数**: 102个
- **最后更新**: 2025-07-30 09:15

## 🔄 更新记录

### 最近更新
- **2025-07-30 09:15** - 新增IntentClassifier、ContextAnalyzer、ContextualSearchManager类，完成第四阶段步骤4.12上下文智能搜索系统开发，16个测试全部通过
- **2025-07-30 08:50** - 新增MultiRoundSearchManager、QueryOptimizer、ResultQualityEvaluator类，完成第四阶段步骤4.11多轮搜索策略开发，19个测试全部通过
- **2025-07-30 08:00** - 扩展ContentProcessor类，添加数据结构化处理功能，包括实体提取、JSON Schema转换、关系提取、格式标准化，完成第四阶段步骤4.9数据结构化处理
- **2025-07-29 21:55** - 扩展ContentProcessor类，添加关键信息提取、摘要生成、数据结构化、重复检测等功能，完成第四阶段步骤4.6内容处理器开发
- **2025-07-29 21:30** - 添加BasicSearchOperation、SearchToolkit类，完成第四阶段步骤4.4和4.5基础搜索操作和功能测试
- **2025-07-29 16:45** - 添加SearchCache、CacheEntry、CacheStats类，完成第四阶段步骤4.3搜索缓存机制开发
- **2025-07-29 18:00** - 添加第四阶段开发规则和预期新增类列表，准备网络搜索集成开发
- **2025-07-29 17:30** - 添加PerformanceOptimizer和PerformanceMonitor类，完成性能优化模块开发
- **2025-07-29 09:06** - 添加TestSystemIntegration测试类，更新TaskSystemIntegrator方法签名，完成系统集成测试
- **2025-07-29 08:00** - 添加FileOperationTaskExecutor和TaskSystemIntegrator类，支持任务系统集成
- **2025-01-29 00:10** - 添加BatchProcessor类，支持批量文件处理功能
- **2025-07-28 22:00** - 添加FormatConverter类，支持多格式文件转换
- **2025-07-28 21:00** - 添加ConfigProcessor类，支持配置文件处理
- **2025-07-28 20:30** - 添加TextAnalyzer类，支持文本分析功能
- **2025-07-28 19:15** - 添加DocumentProcessor类，支持文档处理功能
- **2025-07-28 18:40** - 完成第二周文档处理模块的协调字典更新

---

### 39. MultiRoundSearchManager类 (operations/multi_round_search.py) ✅已完成

**类描述**: 多轮搜索管理器，提供智能多轮搜索策略和质量评估功能

#### Public方法
```python
def __init__(self, config_manager: ConfigManager, search_operations: SearchOperations,
             security_manager: SecurityManager = None, kv_store: KVStore = None) -> None:
    """初始化多轮搜索管理器"""

async def execute_multi_round_search(self, query: str, strategy: SearchStrategy = SearchStrategy.PROGRESSIVE,
                                   max_rounds: int = 3, quality_threshold: float = 0.7,
                                   search_depth: str = "basic", max_results: int = 5) -> MultiRoundSearchResult:
    """执行多轮搜索"""

async def execute_search_round(self, query: str, round_id: int, strategy: SearchStrategy,
                             search_depth: str = "basic", max_results: int = 5,
                             previous_results: List[SearchResult] = None) -> SearchRound:
    """执行单轮搜索"""

def generate_session_id(self) -> str:
    """生成搜索会话ID"""

def get_strategy_params(self, strategy: SearchStrategy, round_id: int) -> Dict[str, Any]:
    """获取策略参数"""

async def get_session_stats(self, session_id: str) -> Dict[str, Any]:
    """获取会话统计信息"""

async def cleanup_old_sessions(self, max_age_hours: int = 24) -> int:
    """清理旧会话数据"""
```

#### Public属性
```python
config_manager: ConfigManager  # 配置管理器
search_operations: SearchOperations  # 搜索操作管理器
query_optimizer: QueryOptimizer  # 查询优化器
quality_evaluator: ResultQualityEvaluator  # 结果质量评估器
security_manager: SecurityManager  # 安全管理器（可选）
kv_store: KVStore  # 键值存储（可选）
session_data: Dict[str, Any]  # 会话数据缓存
```

#### 依赖关系
- 依赖 `ConfigManager` 进行配置管理
- 依赖 `SearchOperations` 进行搜索操作
- 依赖 `QueryOptimizer` 进行查询优化
- 依赖 `ResultQualityEvaluator` 进行质量评估
- 可选依赖 `SecurityManager` 和 `KVStore`
- 使用 `SearchStrategy` 枚举定义搜索策略
- 返回 `MultiRoundSearchResult` 搜索结果

---

### 40. QueryOptimizer类 (operations/multi_round_search.py) ✅已完成

**类描述**: 查询优化器，提供关键词提取、查询扩展和优化功能

#### Public方法
```python
def __init__(self) -> None:
    """初始化查询优化器"""

def extract_keywords(self, query: str) -> List[str]:
    """提取查询关键词"""

def expand_query_synonyms(self, query: str) -> str:
    """使用同义词扩展查询"""

def expand_query_related(self, query: str) -> str:
    """使用相关词扩展查询"""

def expand_query_specific(self, query: str) -> str:
    """使查询更具体化"""

def optimize_query(self, query: str, previous_results: List[SearchResult] = None) -> str:
    """基于历史结果优化查询"""
```

#### Public属性
```python
stop_words: Set[str]  # 停用词集合
synonym_dict: Dict[str, List[str]]  # 同义词字典
related_terms: Dict[str, List[str]]  # 相关词字典
```

---

### 41. ResultQualityEvaluator类 (operations/multi_round_search.py) ✅已完成

**类描述**: 结果质量评估器，提供多维度搜索结果质量评估功能

#### Public方法
```python
def __init__(self) -> None:
    """初始化结果质量评估器"""

def evaluate_relevance(self, query: str, results: List[SearchResult]) -> float:
    """评估结果相关性"""

def evaluate_completeness(self, query: str, results: List[SearchResult]) -> float:
    """评估结果完整性"""

def evaluate_freshness(self, results: List[SearchResult]) -> float:
    """评估结果时效性"""

def evaluate_authority(self, results: List[SearchResult]) -> float:
    """评估结果权威性"""

def evaluate_overall_quality(self, query: str, results: List[SearchResult]) -> float:
    """评估整体质量"""

def get_quality_level(self, score: float) -> SearchQuality:
    """获取质量等级"""
```

#### Public属性
```python
authority_domains: Set[str]  # 权威域名集合
quality_weights: Dict[str, float]  # 质量权重配置
```

---

### 44. IntentClassifier类 (operations/context_search.py) ✅已完成

**类描述**: 用户意图分类器，基于模式匹配和关键词分析识别用户查询意图

#### Public方法
```python
def classify_intent(self, query: str, conversation_context: ConversationContext = None) -> Tuple[UserIntent, float]:
    """分类用户意图

    Args:
        query: 用户查询文本
        conversation_context: 对话上下文（可选）

    Returns:
        Tuple[UserIntent, float]: 意图类型和置信度
    """
```

#### Public属性
```python
intent_patterns: Dict[UserIntent, List[str]]  # 意图模式映射
intent_keywords: Dict[UserIntent, List[str]]  # 意图关键词映射
```

---

### 45. ContextAnalyzer类 (operations/context_search.py) ✅已完成

**类描述**: 上下文分析器，提供实体提取、主题识别和对话上下文分析功能

#### Public方法
```python
def extract_entities(self, text: str) -> Dict[str, List[str]]:
    """提取文本实体

    Args:
        text: 输入文本

    Returns:
        Dict[str, List[str]]: 实体类型到实体列表的映射
    """

def identify_topics(self, text: str) -> List[Tuple[str, float]]:
    """识别文本主题

    Args:
        text: 输入文本

    Returns:
        List[Tuple[str, float]]: 主题和置信度列表
    """

def analyze_conversation_context(self, messages: List[Dict[str, Any]]) -> ConversationContext:
    """分析对话上下文

    Args:
        messages: 对话消息列表

    Returns:
        ConversationContext: 对话上下文对象
    """
```

#### Public属性
```python
kv_store: Optional[KVStore]  # 键值存储（可选）
entity_patterns: Dict[str, List[str]]  # 实体识别模式
topic_keywords: Dict[str, Set[str]]  # 主题关键词映射
```

---

### 46. ContextualSearchManager类 (operations/context_search.py) ✅已完成

**类描述**: 上下文智能搜索管理器，提供基于上下文的智能搜索功能

#### Public方法
```python
async def execute_contextual_search(self, query: str, conversation_messages: List[Dict[str, Any]] = None,
                                  user_id: str = None, search_preferences: Dict[str, Any] = None) -> ContextualSearchResult:
    """执行上下文智能搜索

    Args:
        query: 搜索查询
        conversation_messages: 对话消息（可选）
        user_id: 用户ID（可选）
        search_preferences: 搜索偏好（可选）

    Returns:
        ContextualSearchResult: 上下文搜索结果
    """

async def get_search_history(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
    """获取用户搜索历史

    Args:
        user_id: 用户ID
        limit: 返回数量限制

    Returns:
        List[Dict[str, Any]]: 搜索历史列表
    """

async def cleanup_old_sessions(self, max_age_hours: int = 24) -> int:
    """清理旧的搜索会话

    Args:
        max_age_hours: 最大保留时间（小时）

    Returns:
        int: 清理的会话数量
    """
```

#### Public属性
```python
config_manager: ConfigManager  # 配置管理器
search_operations: SearchOperations  # 搜索操作管理器
multi_round_manager: MultiRoundSearchManager  # 多轮搜索管理器
security_manager: Optional[SecurityManager]  # 安全管理器（可选）
kv_store: Optional[KVStore]  # 键值存储（可选）
intent_classifier: IntentClassifier  # 意图分类器
context_analyzer: ContextAnalyzer  # 上下文分析器
search_sessions: Dict[str, Dict[str, Any]]  # 搜索会话缓存
max_session_age: timedelta  # 最大会话保留时间
```

---

**📌 重要提醒：此文件是多文件开发的核心协调文档，必须严格按照要求维护和更新！**
